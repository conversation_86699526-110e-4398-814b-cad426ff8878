package tanks;

import basewindow.IBatchRenderableObject;
import tanks.obstacle.Obstacle;

public class Chunk
{
    public static class Tile implements IBatchRenderableObject
    {
        public Obstacle obstacle, surfaceObstacle, extraObstacle;
        public double colR, colG, colB, depth, lastHeight;

        /** Whether a tank has spawned on this tile (used during level creation only) */
        public boolean tankSolid;

        public double height()
        {
            return obstacle != null ? obstacle.getTileHeight() : -1000;
        }

        public double edgeDepth()
        {
            return obstacle != null ? obstacle.getEdgeDrawDepth() : 0;
        }

        public double groundHeight()
        {
            return obstacle != null ? obstacle.getGroundHeight() : 0;
        }

        public boolean solid()
        {
            return obstacle != null && obstacle.bulletCollision;
        }

        public boolean unbreakable()
        {
            return obstacle != null && !obstacle.shouldShootThrough;
        }
    }
}
